/**
 * 图标缓存管理器
 * 提供图标URL的缓存和预加载功能
 */

import { ICON_CACHE_CONFIG } from '@/constants/icon.constants';

interface CacheEntry {
  url: string;
  timestamp: number;
  size: number;
  domain: string;
  quality: 'high' | 'medium' | 'low';
}

interface CacheStats {
  totalEntries: number;
  totalSize: number;
  hitRate: number;
  oldestEntry: number;
  newestEntry: number;
}

class IconCacheManager {
  private cache = new Map<string, CacheEntry>();
  private accessCount = new Map<string, number>();
  private hitCount = 0;
  private missCount = 0;

  /**
   * 生成缓存键
   */
  private generateCacheKey(url: string, size: number): string {
    try {
      const urlObj = new URL(url);
      return `${urlObj.hostname}:${size}`;
    } catch {
      return `${url}:${size}`;
    }
  }

  /**
   * 获取缓存的图标URL
   */
  get(url: string, size: number): string | null {
    const key = this.generateCacheKey(url, size);
    const entry = this.cache.get(key);

    if (!entry) {
      this.missCount++;
      return null;
    }

    // 检查是否过期
    const now = Date.now();
    if (now - entry.timestamp > ICON_CACHE_CONFIG.maxAge) {
      this.cache.delete(key);
      this.accessCount.delete(key);
      this.missCount++;
      return null;
    }

    // 更新访问计数
    this.accessCount.set(key, (this.accessCount.get(key) || 0) + 1);
    this.hitCount++;
    
    return entry.url;
  }

  /**
   * 设置缓存
   */
  set(originalUrl: string, faviconUrl: string, size: number, quality: 'high' | 'medium' | 'low' = 'medium'): void {
    const key = this.generateCacheKey(originalUrl, size);
    const now = Date.now();

    try {
      const domain = new URL(originalUrl).hostname;
      const entry: CacheEntry = {
        url: faviconUrl,
        timestamp: now,
        size,
        domain,
        quality,
      };

      this.cache.set(key, entry);
      this.accessCount.set(key, 1);

      // 检查缓存大小并清理
      this.cleanup();
    } catch (error) {
      console.warn('Failed to cache icon:', error);
    }
  }

  /**
   * 清理过期和低优先级的缓存
   */
  private cleanup(): void {
    const now = Date.now();
    const entries = Array.from(this.cache.entries());

    // 移除过期的条目
    for (const [key, entry] of entries) {
      if (now - entry.timestamp > ICON_CACHE_CONFIG.maxAge) {
        this.cache.delete(key);
        this.accessCount.delete(key);
      }
    }

    // 如果缓存仍然太大，移除最少使用的条目
    if (this.cache.size > 100) { // 限制条目数量
      const sortedEntries = Array.from(this.cache.entries())
        .sort((a, b) => {
          const accessA = this.accessCount.get(a[0]) || 0;
          const accessB = this.accessCount.get(b[0]) || 0;
          
          // 优先保留高质量和高访问频率的图标
          if (a[1].quality !== b[1].quality) {
            const qualityScore = { high: 3, medium: 2, low: 1 };
            return qualityScore[b[1].quality] - qualityScore[a[1].quality];
          }
          
          return accessA - accessB;
        });

      // 移除最少使用的25%
      const removeCount = Math.floor(sortedEntries.length * 0.25);
      for (let i = 0; i < removeCount; i++) {
        const [key] = sortedEntries[i];
        this.cache.delete(key);
        this.accessCount.delete(key);
      }
    }
  }

  /**
   * 预加载图标
   */
  async preload(urls: string[], size: number = 32): Promise<void> {
    const loadPromises = urls.map(async (url) => {
      const key = this.generateCacheKey(url, size);
      
      // 如果已经缓存，跳过
      if (this.cache.has(key)) {
        return;
      }

      try {
        const { loadBestFaviconUrl } = await import('./icon-utils');
        const faviconUrl = await loadBestFaviconUrl(url, size);
        
        if (faviconUrl) {
          this.set(url, faviconUrl, size, 'medium');
        }
      } catch (error) {
        console.warn(`Failed to preload icon for ${url}:`, error);
      }
    });

    await Promise.allSettled(loadPromises);
  }

  /**
   * 获取缓存统计信息
   */
  getStats(): CacheStats {
    const entries = Array.from(this.cache.values());
    const timestamps = entries.map(e => e.timestamp);
    
    return {
      totalEntries: this.cache.size,
      totalSize: entries.reduce((sum, entry) => sum + entry.size, 0),
      hitRate: this.hitCount + this.missCount > 0 
        ? this.hitCount / (this.hitCount + this.missCount) 
        : 0,
      oldestEntry: timestamps.length > 0 ? Math.min(...timestamps) : 0,
      newestEntry: timestamps.length > 0 ? Math.max(...timestamps) : 0,
    };
  }

  /**
   * 清空缓存
   */
  clear(): void {
    this.cache.clear();
    this.accessCount.clear();
    this.hitCount = 0;
    this.missCount = 0;
  }

  /**
   * 获取特定域名的缓存条目
   */
  getDomainEntries(domain: string): CacheEntry[] {
    return Array.from(this.cache.values())
      .filter(entry => entry.domain === domain);
  }

  /**
   * 移除特定域名的缓存
   */
  removeDomainEntries(domain: string): void {
    const keysToRemove: string[] = [];
    
    for (const [key, entry] of this.cache.entries()) {
      if (entry.domain === domain) {
        keysToRemove.push(key);
      }
    }
    
    keysToRemove.forEach(key => {
      this.cache.delete(key);
      this.accessCount.delete(key);
    });
  }

  /**
   * 导出缓存数据（用于持久化）
   */
  export(): Record<string, CacheEntry> {
    return Object.fromEntries(this.cache.entries());
  }

  /**
   * 导入缓存数据（用于恢复）
   */
  import(data: Record<string, CacheEntry>): void {
    this.clear();
    
    const now = Date.now();
    for (const [key, entry] of Object.entries(data)) {
      // 只导入未过期的条目
      if (now - entry.timestamp < ICON_CACHE_CONFIG.maxAge) {
        this.cache.set(key, entry);
        this.accessCount.set(key, 1);
      }
    }
  }
}

// 创建全局缓存实例
export const iconCache = new IconCacheManager();

// 定期清理缓存
if (typeof window !== 'undefined') {
  setInterval(() => {
    iconCache['cleanup']();
  }, ICON_CACHE_CONFIG.cleanupInterval);
}

export default IconCacheManager;
