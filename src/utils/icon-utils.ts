/**
 * 图标获取工具函数
 * 处理各种图标获取方式，包括fallback机制
 */

import type { Bookmark } from '../types';
import { FAVICON_SERVICE_URLS, ICON_QUALITY_CONFIG, ICON_RETRY_CONFIG } from '../constants/icon.constants';

/**
 * 从URL中提取域名
 */
export function extractDomain(url: string): string {
  try {
    const urlObj = new URL(url);
    return urlObj.hostname;
  } catch (error) {
    // 如果URL解析失败，尝试简单的域名提取
    const match = url.match(/^(?:https?:\/\/)?(?:www\.)?([^\/\?#]+)/i);
    return match ? match[1] : url;
  }
}

/**
 * 检查是否为内网地址
 */
export function isInternalDomain(domain: string): boolean {
  const internalPatterns = [
    /^localhost$/i,
    /^127\.0\.0\.1$/,
    /^192\.168\./,
    /^10\./,
    /^172\.(1[6-9]|2[0-9]|3[0-1])\./,
    /\.local$/i,
    /\.lan$/i,
  ];

  return internalPatterns.some(pattern => pattern.test(domain));
}

/**
 * 获取网站favicon的最佳URL（智能选择）
 */
export function getFaviconUrl(url: string, size: number = 32): string {
  try {
    const domain = extractDomain(url);

    // 检查是否有自定义图标映射
    const customIcon = getCustomDomainIcon(domain);
    if (customIcon) {
      return customIcon;
    }

    // 对于内网地址，优先使用快速服务
    if (isInternalDomain(domain)) {
      return ICON_QUALITY_CONFIG.fastFallbackServices[0]
        .replace('{domain}', domain)
        .replace('{size}', size.toString());
    }

    // 对于外网地址，优先使用高质量服务
    return ICON_QUALITY_CONFIG.highQualityServices[0]
      .replace('{domain}', domain)
      .replace('{size}', size.toString());
  } catch (error) {
    console.warn('无法解析URL获取favicon:', url, error);
    return '';
  }
}

/**
 * 获取自定义域名图标
 */
export function getCustomDomainIcon(domain: string): string | null {
  const customIcons = ICON_QUALITY_CONFIG.customDomainIcons;

  // 精确匹配
  if (customIcons[domain]) {
    return customIcons[domain];
  }

  // 前缀匹配（用于内网地址段）
  for (const [prefix, icon] of Object.entries(customIcons)) {
    if (prefix.endsWith('.') && domain.startsWith(prefix)) {
      return icon;
    }
  }

  return null;
}

/**
 * 获取网站favicon的备用URL列表（智能排序）
 */
export function getFaviconFallbackUrls(url: string, size: number = 32): string[] {
  try {
    const domain = extractDomain(url);

    // 检查是否有自定义图标
    const customIcon = getCustomDomainIcon(domain);
    if (customIcon) {
      return [customIcon];
    }

    const isInternal = isInternalDomain(domain);

    // 根据域名类型选择不同的策略
    if (isInternal) {
      // 内网地址：优先使用快速服务和直接访问
      return [
        ...ICON_QUALITY_CONFIG.fastFallbackServices,
        `https://${domain}/apple-touch-icon.png`,
        `https://${domain}/android-chrome-192x192.png`,
      ].map(template =>
        template
          .replace('{domain}', domain)
          .replace('{size}', size.toString())
      );
    } else {
      // 外网地址：优先使用高质量服务，然后是快速备用
      return [
        ...ICON_QUALITY_CONFIG.highQualityServices,
        ...ICON_QUALITY_CONFIG.fastFallbackServices,
        `https://${domain}/apple-touch-icon.png`,
        `https://${domain}/android-chrome-192x192.png`,
        `https://favicon.yandex.net/favicon/${domain}`,
      ].map(template =>
        template
          .replace('{domain}', domain)
          .replace('{size}', size.toString())
      ).filter((url, index, array) => array.indexOf(url) === index); // 去重
    }
  } catch (error) {
    console.warn('无法解析URL获取favicon备用列表:', url, error);
    return [];
  }
}

/**
 * 并发加载图标，返回第一个成功的URL（带缓存）
 */
export async function loadBestFaviconUrl(url: string, size: number = 32): Promise<string | null> {
  // 尝试从缓存获取
  const { iconCache } = await import('./icon-cache');
  const cachedUrl = iconCache.get(url, size);
  if (cachedUrl) {
    return cachedUrl;
  }

  const fallbackUrls = getFaviconFallbackUrls(url, size);

  if (fallbackUrls.length === 0) {
    return null;
  }

  // 如果是自定义图标（emoji等），直接返回并缓存
  if (fallbackUrls.length === 1 && !fallbackUrls[0].startsWith('http')) {
    iconCache.set(url, fallbackUrls[0], size, 'high');
    return fallbackUrls[0];
  }

  // 创建加载Promise数组
  const loadPromises = fallbackUrls.map((iconUrl, index) =>
    new Promise<{ url: string; index: number; quality: 'high' | 'medium' | 'low' }>((resolve, reject) => {
      const img = new Image();
      const timeoutId = setTimeout(() => {
        reject(new Error(`Timeout loading ${iconUrl}`));
      }, ICON_RETRY_CONFIG.timeoutMs);

      img.onload = () => {
        clearTimeout(timeoutId);
        // 检查图片是否有效（不是1x1的透明图片）
        if (img.width > 1 && img.height > 1) {
          // 根据服务质量和图片尺寸评估质量
          let quality: 'high' | 'medium' | 'low' = 'medium';

          if (index < 3) { // 前3个服务认为是高质量
            quality = 'high';
          } else if (img.width >= 32 && img.height >= 32) {
            quality = 'medium';
          } else {
            quality = 'low';
          }

          resolve({ url: iconUrl, index, quality });
        } else {
          reject(new Error(`Invalid image size: ${img.width}x${img.height}`));
        }
      };

      img.onerror = () => {
        clearTimeout(timeoutId);
        reject(new Error(`Failed to load ${iconUrl}`));
      };

      img.src = iconUrl;
    })
  );

  try {
    // 使用Promise.any获取第一个成功的结果
    const result = await Promise.any(loadPromises);

    // 缓存成功的结果
    iconCache.set(url, result.url, size, result.quality);

    return result.url;
  } catch (error) {
    console.warn('所有图标加载失败:', error);
    return null;
  }
}

/**
 * 预加载图标并缓存
 */
export function preloadFavicon(url: string, size: number = 32): Promise<string | null> {
  return loadBestFaviconUrl(url, size);
}

/**
 * 批量预加载书签图标
 */
export async function preloadBookmarkIcons(bookmarks: Array<{ url: string; title: string }>, size: number = 32): Promise<void> {
  const { iconCache } = await import('./icon-cache');
  const urls = bookmarks.map(b => b.url);
  await iconCache.preload(urls, size);
}

/**
 * 获取图标缓存统计信息
 */
export async function getIconCacheStats() {
  const { iconCache } = await import('./icon-cache');
  return iconCache.getStats();
}

/**
 * 清理图标缓存
 */
export async function clearIconCache(): Promise<void> {
  const { iconCache } = await import('./icon-cache');
  iconCache.clear();
}

/**
 * 创建一个带有错误处理的图标组件
 */
export function createFaviconElement(
  url: string, 
  alt: string, 
  className: string = 'w-8 h-8',
  onError?: () => void
): HTMLImageElement {
  const img = document.createElement('img');
  const fallbackUrls = getFaviconFallbackUrls(url);
  let currentUrlIndex = 0;

  const tryNextUrl = () => {
    if (currentUrlIndex < fallbackUrls.length) {
      img.src = fallbackUrls[currentUrlIndex];
      currentUrlIndex++;
    } else {
      // 所有URL都失败了，触发最终的错误处理
      onError?.();
    }
  };

  img.onerror = tryNextUrl;
  img.alt = alt;
  img.className = className;
  
  // 开始尝试第一个URL
  tryNextUrl();
  
  return img;
}

/**
 * 获取书签的最佳图标URL
 */
export function getBookmarkIconUrl(bookmark: Bookmark, networkMode: 'internal' | 'external'): string {
  // 根据网络模式选择URL
  let url = bookmark.url;
  if (networkMode === 'internal' && bookmark.internalUrl) {
    url = bookmark.internalUrl;
  } else if (networkMode === 'external' && bookmark.externalUrl) {
    url = bookmark.externalUrl;
  }

  switch (bookmark.iconType) {
    case 'upload':
      return bookmark.iconData || bookmark.icon || '';
    
    case 'text':
      // 文字图标不需要URL
      return '';
    
    case 'official':
    default:
      return getFaviconUrl(url);
  }
}

/**
 * 预加载图标
 */
export function preloadIcon(url: string): Promise<boolean> {
  return new Promise((resolve) => {
    if (!url) {
      resolve(false);
      return;
    }

    const img = new Image();
    img.onload = () => resolve(true);
    img.onerror = () => resolve(false);
    img.src = url;
  });
}

/**
 * 批量预加载书签图标
 */
export async function preloadBookmarkIcons(
  bookmarks: Bookmark[], 
  networkMode: 'internal' | 'external'
): Promise<{ success: number; failed: number }> {
  const results = await Promise.allSettled(
    bookmarks.map(bookmark => {
      const iconUrl = getBookmarkIconUrl(bookmark, networkMode);
      return preloadIcon(iconUrl);
    })
  );

  const success = results.filter(r => r.status === 'fulfilled' && r.value).length;
  const failed = results.length - success;

  return { success, failed };
}
