/**
 * 书签图标组件
 * 支持文字、图片、favicon三种类型
 */

import React from 'react';
import { colorWithOpacity } from '@/utils/gradient/customGradientUtils';
import type { BookmarkItem } from '@/types/bookmark-style.types';

interface BookmarkIconProps {
  bookmark: BookmarkItem;
  size: number;
  borderRadius: number;
  className?: string;
}

const BookmarkIcon: React.FC<BookmarkIconProps> = ({
  bookmark,
  size,
  borderRadius,
  className = '',
}) => {
  const iconStyle: React.CSSProperties = {
    width: size,
    height: size,
    borderRadius: `${borderRadius}px`,
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    fontSize: Math.round(size * 0.4),
    fontWeight: 'bold',
    color: 'white',
    overflow: 'hidden',
    flexShrink: 0,
  };

  // 渲染文字图标
  if (bookmark.iconType === 'text') {
    const backgroundColor = bookmark.iconColor || '#3b82f6';
    const text = bookmark.iconText || bookmark.title.slice(0, 2);
    
    return (
      <div
        className={`${className}`}
        style={{
          ...iconStyle,
          backgroundColor,
        }}
      >
        <span style={{
          lineHeight: 1,
          whiteSpace: 'nowrap',
          fontSize: text.length > 2 ? Math.round(size * 0.3) : Math.round(size * 0.4),
        }}>
          {text}
        </span>
      </div>
    );
  }

  // 渲染图片图标
  if (bookmark.iconType === 'image' && bookmark.iconImage) {
    // 获取背景颜色配置
    const backgroundColor = bookmark.imageScale?.backgroundColor;
    const backgroundOpacity = bookmark.imageScale?.backgroundOpacity ?? 100;
    
    // 处理背景颜色：如果没有设置或透明度为0，则使用透明背景
    const rgbaBackground = backgroundColor && backgroundOpacity > 0
      ? colorWithOpacity(backgroundColor, backgroundOpacity)
      : 'transparent';
    
    return (
      <div
        className={`${className}`}
        style={{
          ...iconStyle,
          backgroundColor: rgbaBackground,
        }}
      >
        <img
          src={bookmark.iconImage}
          alt={bookmark.title}
          style={{
            width: '100%',
            height: '100%',
            objectFit: 'cover',
            borderRadius: `${borderRadius}px`,
          }}
          onError={(e) => {
            // 图片加载失败时显示文字
            const target = e.target as HTMLImageElement;
            const parent = target.parentElement;
            if (parent) {
              parent.innerHTML = bookmark.title.slice(0, 2);
              parent.style.backgroundColor = bookmark.iconColor || '#3b82f6';
            }
          }}
        />
      </div>
    );
  }

  // 渲染favicon/official图标 - 使用增强的图标组件
  if (bookmark.iconType === 'favicon' || bookmark.iconType === 'official' || !bookmark.iconType) {
    // 动态导入增强的图标组件
    const EnhancedFaviconIcon = React.lazy(() => import('./EnhancedFaviconIcon'));

    return (
      <React.Suspense
        fallback={
          <div
            className={`${className} bg-gray-100 animate-pulse`}
            style={iconStyle}
          >
            <div className="w-4 h-4 bg-gray-300 rounded"></div>
          </div>
        }
      >
        <EnhancedFaviconIcon
          url={bookmark.url}
          title={bookmark.title}
          size={size}
          borderRadius={borderRadius}
          className={className}
          fallbackToText={true}
          showLoadingState={true}
        />
      </React.Suspense>
    );
  }

  // 默认文字图标
  return (
    <div
      className={`${className}`}
      style={{
        ...iconStyle,
        backgroundColor: bookmark.iconColor || '#3b82f6',
      }}
    >
      {bookmark.title.slice(0, 2)}
    </div>
  );
};

export default BookmarkIcon;
