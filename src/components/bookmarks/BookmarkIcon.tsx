/**
 * 书签图标组件
 * 支持文字、图片、favicon三种类型
 */

import React from 'react';
import { colorWithOpacity } from '@/utils/gradient/customGradientUtils';
import type { BookmarkItem } from '@/types/bookmark-style.types';

interface BookmarkIconProps {
  bookmark: BookmarkItem;
  size: number;
  borderRadius: number;
  className?: string;
}

const BookmarkIcon: React.FC<BookmarkIconProps> = ({
  bookmark,
  size,
  borderRadius,
  className = '',
}) => {
  const iconStyle: React.CSSProperties = {
    width: size,
    height: size,
    borderRadius: `${borderRadius}px`,
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    fontSize: Math.round(size * 0.4),
    fontWeight: 'bold',
    color: 'white',
    overflow: 'hidden',
    flexShrink: 0,
  };

  // 渲染文字图标
  if (bookmark.iconType === 'text') {
    const backgroundColor = bookmark.iconColor || '#3b82f6';
    const text = bookmark.iconText || bookmark.title.slice(0, 2);
    
    return (
      <div
        className={`${className}`}
        style={{
          ...iconStyle,
          backgroundColor,
        }}
      >
        <span style={{
          lineHeight: 1,
          whiteSpace: 'nowrap',
          fontSize: text.length > 2 ? Math.round(size * 0.3) : Math.round(size * 0.4),
        }}>
          {text}
        </span>
      </div>
    );
  }

  // 渲染图片图标
  if (bookmark.iconType === 'image' && bookmark.iconImage) {
    // 获取背景颜色配置
    const backgroundColor = bookmark.imageScale?.backgroundColor;
    const backgroundOpacity = bookmark.imageScale?.backgroundOpacity ?? 100;
    
    // 处理背景颜色：如果没有设置或透明度为0，则使用透明背景
    const rgbaBackground = backgroundColor && backgroundOpacity > 0
      ? colorWithOpacity(backgroundColor, backgroundOpacity)
      : 'transparent';
    
    return (
      <div
        className={`${className}`}
        style={{
          ...iconStyle,
          backgroundColor: rgbaBackground,
        }}
      >
        <img
          src={bookmark.iconImage}
          alt={bookmark.title}
          style={{
            width: '100%',
            height: '100%',
            objectFit: 'cover',
            borderRadius: `${borderRadius}px`,
          }}
          onError={(e) => {
            // 图片加载失败时显示文字
            const target = e.target as HTMLImageElement;
            const parent = target.parentElement;
            if (parent) {
              parent.innerHTML = bookmark.title.slice(0, 2);
              parent.style.backgroundColor = bookmark.iconColor || '#3b82f6';
            }
          }}
        />
      </div>
    );
  }

  // 渲染favicon/official图标 - 简化版本
  if (bookmark.iconType === 'favicon' || bookmark.iconType === 'official' || !bookmark.iconType) {
    const { extractDomain, isInternalDomain } = require('@/utils/icon-utils');
    const domain = extractDomain(bookmark.url);

    // 内网地址显示特殊图标
    if (isInternalDomain(domain)) {
      return (
        <div
          className={`${className} bg-orange-50 text-orange-600 font-medium`}
          style={{
            ...iconStyle,
            fontSize: size * 0.5,
          }}
        >
          🏠
        </div>
      );
    }

    // 使用Google Favicons API
    const faviconUrl = `https://www.google.com/s2/favicons?domain=${domain}&sz=${Math.min(size, 64)}`;

    return (
      <div
        className={`${className} bg-white`}
        style={{
          ...iconStyle,
          padding: size * 0.1,
        }}
      >
        <img
          src={faviconUrl}
          alt={bookmark.title}
          style={{
            width: '100%',
            height: '100%',
            objectFit: 'contain',
            borderRadius: `${borderRadius * 0.5}px`,
          }}
          onError={(e) => {
            // favicon加载失败时显示文字
            const target = e.target as HTMLImageElement;
            const parent = target.parentElement;
            if (parent) {
              parent.innerHTML = bookmark.title.slice(0, 2).toUpperCase();
              parent.style.backgroundColor = bookmark.iconColor || '#3b82f6';
              parent.style.color = 'white';
              parent.style.padding = '0';
              parent.style.fontSize = `${size * 0.35}px`;
            }
          }}
        />
      </div>
    );
  }

  // 默认文字图标
  return (
    <div
      className={`${className}`}
      style={{
        ...iconStyle,
        backgroundColor: bookmark.iconColor || '#3b82f6',
      }}
    >
      {bookmark.title.slice(0, 2)}
    </div>
  );
};

export default BookmarkIcon;
